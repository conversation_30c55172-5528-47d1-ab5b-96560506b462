"use client";

import { fabric } from "fabric";
import debounce from "lodash.debounce";
import { useCallback, useEffect, useRef, useState, memo } from "react";

import { ResponseType } from "@/features/projects/api/use-get-project";
import { useUpdateProject } from "@/features/projects/api/use-update-project";
import { useThumbnailGenerator } from "@/features/editor/hooks/use-thumbnail-generator";

import { 
  ActiveTool, 
  selectionDependentTools
} from "@/features/editor/types";
import { Navbar } from "@/features/editor/components/navbar";
import { Footer } from "@/features/editor/components/footer";
import { useEditor } from "@/features/editor/hooks/use-editor";
import { Sidebar } from "@/features/editor/components/sidebar";
import { Toolbar } from "@/features/editor/components/toolbar";
import { ShapeSidebar } from "@/features/editor/components/shape-sidebar";
import { FillColorSidebar } from "@/features/editor/components/fill-color-sidebar";
import { StrokeColorSidebar } from "@/features/editor/components/stroke-color-sidebar";
import { StrokeWidthSidebar } from "@/features/editor/components/stroke-width-sidebar";
import { OpacitySidebar } from "@/features/editor/components/opacity-sidebar";
import { TextSidebar } from "@/features/editor/components/text-sidebar";
import { FontSidebar } from "@/features/editor/components/font-sidebar";
import { ImageSidebar } from "@/features/editor/components/image-sidebar";
import { FilterSidebar } from "@/features/editor/components/filter-sidebar";
import { DrawSidebar } from "@/features/editor/components/draw-sidebar";
import { AiSidebar } from "@/features/editor/components/ai-sidebar";
import { AiToolsSidebar } from "@/features/editor/components/ai-tools-sidebar";
import { TemplateSidebar } from "@/features/editor/components/template-sidebar";
import { RemoveBgSidebar } from "@/features/editor/components/remove-bg-sidebar";
import { SettingsSidebar } from "@/features/editor/components/settings-sidebar";
import { TemplateConfigSidebar } from "@/features/editor/components/template-config-sidebar";

interface EditorProps {
  initialData: ResponseType["data"];
  initialActiveTool?: ActiveTool;
};

const EditorComponent = ({ initialData, initialActiveTool = "select" }: EditorProps) => {
  const { mutate } = useUpdateProject(initialData.id);

  // Template Editor Logging - Component Mount
  useEffect(() => {
    console.log('🎨 [TEMPLATE EDITOR] Component mounted with initial data:', {
      projectId: initialData.id,
      projectName: initialData.name,
      width: initialData.width,
      height: initialData.height,
      hasJson: !!initialData.json,
      jsonLength: initialData.json?.length || 0,
      isCustomizable: initialData.isCustomizable,
      editableLayersCount: initialData.editableLayers?.length || 0,
      initialActiveTool,
      timestamp: new Date().toISOString()
    });

    // Try to parse and log JSON structure
    if (initialData.json) {
      try {
        const parsedJson = JSON.parse(initialData.json);
        console.log('🎨 [TEMPLATE EDITOR] Initial JSON structure:', {
          version: parsedJson.version,
          objectCount: parsedJson.objects?.length || 0,
          objects: parsedJson.objects?.map((obj: any) => ({
            type: obj.type,
            id: obj.id,
            name: obj.name
          })) || []
        });
      } catch (error) {
        console.error('🎨 [TEMPLATE EDITOR] Error parsing initial JSON:', error);
      }
    }

    return () => {
      console.log('🎨 [TEMPLATE EDITOR] Component unmounting');
    };
  }, []);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSave = useCallback(
    debounce(
      (values: {
        json: string,
        height: number,
        width: number,
      }) => {
        console.log('🎨 [TEMPLATE EDITOR] Debounced save triggered:', {
          width: values.width,
          height: values.height,
          jsonLength: values.json.length,
          timestamp: new Date().toISOString()
        });
        mutate(values);
    },
    5000 // Increased to 5 seconds for better performance
  ), [mutate]);

  const [activeTool, setActiveTool] = useState<ActiveTool>(initialActiveTool);

  const onClearSelection = useCallback(() => {
    if (selectionDependentTools.includes(activeTool)) {
      console.log('🎨 [TEMPLATE EDITOR] Clearing selection, changing tool from', activeTool, 'to select');
      setActiveTool("select");
    }
  }, [activeTool]);

  console.log('🎨 [TEMPLATE EDITOR] Initializing editor hook...');
  const { init, editor } = useEditor({
    defaultState: initialData.json,
    defaultWidth: initialData.width,
    defaultHeight: initialData.height,
    clearSelectionCallback: onClearSelection,
    saveCallback: debouncedSave,
  });

  const handleManualSave = () => {
    console.log('🎨 [TEMPLATE EDITOR] Manual save triggered');
    if (editor?.canvas) {
      const workspace = editor.canvas
        .getObjects()
        .find((object) => object.name === "clip");
      const height = workspace?.height || initialData.height;
      const width = workspace?.width || initialData.width;
      const json = JSON.stringify(editor.canvas.toJSON());

      console.log('🎨 [TEMPLATE EDITOR] Manual save data:', {
        width,
        height,
        jsonLength: json.length,
        objectCount: editor.canvas.getObjects().length,
        timestamp: new Date().toISOString()
      });

      mutate({ json, height, width });
    } else {
      console.warn('🎨 [TEMPLATE EDITOR] Manual save failed - no editor or canvas available');
    }
  };

  // Generate thumbnails automatically when the canvas changes
  const { debouncedGenerateThumbnail } = useThumbnailGenerator({
    editor,
    projectId: initialData.id,
  });

  // Template Editor Logging - Editor State Changes
  useEffect(() => {
    if (editor) {
      console.log('🎨 [TEMPLATE EDITOR] Editor initialized:', {
        hasCanvas: !!editor.canvas,
        hasAutoZoom: !!editor.autoZoom,
        canvasObjects: editor.canvas?.getObjects().length || 0,
        canUndo: editor.canUndo(),
        canRedo: editor.canRedo(),
        timestamp: new Date().toISOString()
      });

      // Log canvas dimensions
      if (editor.canvas) {
        console.log('🎨 [TEMPLATE EDITOR] Canvas dimensions:', {
          width: editor.canvas.width,
          height: editor.canvas.height,
          zoom: editor.canvas.getZoom(),
          timestamp: new Date().toISOString()
        });
      }
    }
  }, [editor]);

  // Trigger thumbnail generation when canvas changes
  useEffect(() => {
    if (editor?.canvas) {
      const handleCanvasChange = () => {
        console.log('🎨 [TEMPLATE EDITOR] Canvas change detected, generating thumbnail');
        debouncedGenerateThumbnail();
      };

      const canvas = editor.canvas;
      canvas.on('object:added', handleCanvasChange);
      canvas.on('object:removed', handleCanvasChange);
      canvas.on('object:modified', handleCanvasChange);
      canvas.on('path:created', handleCanvasChange);

      console.log('🎨 [TEMPLATE EDITOR] Canvas event listeners attached');

      // Generate initial thumbnail when editor is ready (only once)
      setTimeout(() => {
        console.log('🎨 [TEMPLATE EDITOR] Generating initial thumbnail after 2s delay');
        debouncedGenerateThumbnail();
      }, 2000); // Wait for canvas to be fully ready

      return () => {
        console.log('🎨 [TEMPLATE EDITOR] Removing canvas event listeners');
        canvas.off('object:added', handleCanvasChange);
        canvas.off('object:removed', handleCanvasChange);
        canvas.off('object:modified', handleCanvasChange);
        canvas.off('path:created', handleCanvasChange);
      };
    }
  }, [editor, debouncedGenerateThumbnail]);

  // Trigger thumbnail generation when editor changes
  useEffect(() => {
    if (editor) {
      console.log('🎨 [TEMPLATE EDITOR] Editor changed, generating thumbnail');
      debouncedGenerateThumbnail();
    }
  }, [editor, debouncedGenerateThumbnail]);

  // Track when component is fully mounted and ready (with debounce)
  const readyTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  useEffect(() => {
    if (editor?.canvas && editor.canvas.getObjects().length > 1) {
      // Clear any existing timeout
      if (readyTimeoutRef.current) {
        clearTimeout(readyTimeoutRef.current);
      }

      // Wait a bit to ensure everything is settled
      readyTimeoutRef.current = setTimeout(() => {
        console.log('🎨 [TEMPLATE EDITOR] 🎯 COMPONENT FULLY MOUNTED AND INTERACTIVE 🎯:', {
          projectId: initialData.id,
          projectName: initialData.name,
          canvasObjects: editor.canvas.getObjects().length,
          canvasSize: { width: editor.canvas.width, height: editor.canvas.height },
          zoom: editor.canvas.getZoom(),
          activeTool,
          timestamp: new Date().toISOString()
        });
        readyTimeoutRef.current = null;
      }, 1000);

      return () => {
        if (readyTimeoutRef.current) {
          clearTimeout(readyTimeoutRef.current);
          readyTimeoutRef.current = null;
        }
      };
    }
  }, [editor, activeTool, initialData.id, initialData.name]);

  const onChangeActiveTool = useCallback((tool: ActiveTool) => {
    console.log('🎨 [TEMPLATE EDITOR] Active tool changing:', {
      from: activeTool,
      to: tool,
      timestamp: new Date().toISOString()
    });

    if (tool === "draw") {
      console.log('🎨 [TEMPLATE EDITOR] Enabling draw mode');
      editor?.enableDrawingMode();
    }

    if (activeTool === "draw") {
      console.log('🎨 [TEMPLATE EDITOR] Disabling draw mode');
      editor?.disableDrawingMode();
    }

    if (tool === "pan") {
      console.log('🎨 [TEMPLATE EDITOR] Enabling pan mode');
      editor?.enablePanMode();
    }

    if (activeTool === "pan") {
      console.log('🎨 [TEMPLATE EDITOR] Disabling pan mode');
      editor?.disablePanMode();
    }

    if (tool === activeTool) {
      console.log('🎨 [TEMPLATE EDITOR] Same tool selected, switching to select');
      return setActiveTool("select");
    }

    setActiveTool(tool);
  }, [activeTool, editor]);

  const canvasRef = useRef(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isInitializedRef = useRef(false);
  const initTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Global flag to prevent any re-initialization after first success
  const globalInitializedRef = useRef(false);

  useEffect(() => {
    console.log('🎨 [TEMPLATE EDITOR] Canvas initialization effect triggered');

    // STOP ALL RE-INITIALIZATIONS after first success
    if (globalInitializedRef.current) {
      console.log('🎨 [TEMPLATE EDITOR] ❌ BLOCKING RE-INITIALIZATION - Canvas already successfully initialized globally');
      return;
    }

    // Prevent multiple initializations in this component instance
    if (isInitializedRef.current) {
      console.log('🎨 [TEMPLATE EDITOR] Canvas already initialized in this instance, skipping...');
      return;
    }

    const initializeCanvas = () => {
      console.log('🎨 [TEMPLATE EDITOR] Attempting canvas initialization...');

      // Ensure container is available and has dimensions
      if (!containerRef.current) {
        console.warn('🎨 [TEMPLATE EDITOR] Container ref not available');
        return false;
      }

      const container = containerRef.current;
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;

      console.log('🎨 [TEMPLATE EDITOR] Container dimensions:', {
        width: containerWidth,
        height: containerHeight,
        timestamp: new Date().toISOString()
      });

      // Don't initialize if container has no dimensions
      if (containerWidth === 0 || containerHeight === 0) {
        console.warn("🎨 [TEMPLATE EDITOR] Container has zero dimensions, delaying canvas initialization");
        return false;
      }

      console.log('🎨 [TEMPLATE EDITOR] Creating fabric canvas...');
      const canvas = new fabric.Canvas(canvasRef.current, {
        controlsAboveOverlay: true,
        preserveObjectStacking: true,
      });

      console.log('🎨 [TEMPLATE EDITOR] Fabric canvas created, initializing editor...');
      init({
        initialCanvas: canvas,
        initialContainer: container,
      });

      console.log('🎨 [TEMPLATE EDITOR] ✅ Canvas initialization completed successfully - SETTING GLOBAL LOCK');
      isInitializedRef.current = true;
      globalInitializedRef.current = true; // GLOBAL LOCK - no more initializations allowed
      return canvas;
    };

    // Clear any existing timeout
    if (initTimeoutRef.current) {
      clearTimeout(initTimeoutRef.current);
      initTimeoutRef.current = null;
    }

    // Try to initialize immediately
    console.log('🎨 [TEMPLATE EDITOR] Starting immediate canvas initialization');
    const canvas = initializeCanvas();

    if (!canvas) {
      console.log('🎨 [TEMPLATE EDITOR] Initial canvas initialization failed, scheduling retry...');
      // If initialization failed, retry after a short delay
      initTimeoutRef.current = setTimeout(() => {
        console.log('🎨 [TEMPLATE EDITOR] Retrying canvas initialization...');
        const retryCanvas = initializeCanvas();
        if (!retryCanvas) {
          console.error("🎨 [TEMPLATE EDITOR] Failed to initialize canvas after retry");
        } else {
          console.log('🎨 [TEMPLATE EDITOR] ✅ Retry successful - SETTING GLOBAL LOCK');
          globalInitializedRef.current = true; // GLOBAL LOCK - no more initializations allowed
        }
        initTimeoutRef.current = null;
      }, 100);

      return () => {
        if (initTimeoutRef.current) {
          clearTimeout(initTimeoutRef.current);
          initTimeoutRef.current = null;
        }
      };
    }

    return () => {
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current);
        initTimeoutRef.current = null;
      }

      // DO NOT dispose canvas or reset flags if globally initialized
      if (globalInitializedRef.current) {
        console.log('🎨 [TEMPLATE EDITOR] ❌ PREVENTING CANVAS DISPOSAL - Global lock active');
        return;
      }

      if (canvas) {
        console.log('🎨 [TEMPLATE EDITOR] Disposing canvas on cleanup');
        canvas.dispose();
      }
      // Reset initialization flag on cleanup
      isInitializedRef.current = false;
    };
  }, [init]);

  // Log when component is about to render
  console.log('🎨 [TEMPLATE EDITOR] Component rendering with:', {
    hasEditor: !!editor,
    activeTool,
    canvasReady: !!editor?.canvas,
    timestamp: new Date().toISOString()
  });

  return (
    <div className="h-full flex flex-col">
      <Navbar
        id={initialData.id}
        editor={editor}
        activeTool={activeTool}
        onChangeActiveTool={onChangeActiveTool}
        onSave={handleManualSave}
        initialData={{
          name: initialData.name,
          isCustomizable: initialData.isCustomizable || false,
        }}
      />
      <div className="absolute h-[calc(100%-68px)] w-full top-[68px] flex">
        <Sidebar
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <ShapeSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <FillColorSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <StrokeColorSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <StrokeWidthSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <OpacitySidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <TextSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <FontSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <ImageSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <TemplateSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <FilterSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <AiSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <AiToolsSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <RemoveBgSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <DrawSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <SettingsSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <TemplateConfigSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
          projectId={initialData.id}
          initialData={{
            isCustomizable: initialData.isCustomizable || false,
            editableLayers: initialData.editableLayers,
          }}
        />
        <main className="bg-muted flex-1 overflow-auto relative flex flex-col">
          <Toolbar
            editor={editor}
            activeTool={activeTool}
            onChangeActiveTool={onChangeActiveTool}
            key={JSON.stringify(editor?.canvas.getActiveObject())}
          />
          <div className="flex-1 h-[calc(100%-124px)] bg-muted" ref={containerRef}>
            <canvas ref={canvasRef} />
          </div>
          <Footer editor={editor} />
        </main>
      </div>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const Editor = memo(EditorComponent);
