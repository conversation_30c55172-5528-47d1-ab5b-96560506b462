hook.js:608 Warning: Extra attributes from the server: webcrx Error Component Stack
    at html (<anonymous>)
    at SessionProvider (react.js:251:13)
    at RootLayout [Server] (<anonymous>)
    at RedirectErrorBoundary (redirect-boundary.js:74:9)
    at RedirectBoundary (redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (not-found-boundary.js:76:9)
    at NotFoundBoundary (not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (ReactDevOverlay.js:87:9)
    at HotReload (hot-reloader-client.js:321:11)
    at Router (app-router.js:207:11)
    at ErrorBoundaryHandler (error-boundary.js:113:9)
    at ErrorBoundary (error-boundary.js:160:11)
    at AppRouter (app-router.js:585:13)
    at ServerRoot (app-index.js:112:27)
    at Root (app-index.js:117:11)
overrideMethod @ hook.js:608
window.console.error @ app-index.js:33
console.error @ hydration-error-info.js:63
printWarning @ react-dom.development.js:94
error @ react-dom.development.js:68
warnForExtraAttributes @ react-dom.development.js:32731
diffHydratedProperties @ react-dom.development.js:35117
hydrateInstance @ react-dom.development.js:36127
prepareToHydrateHostInstance @ react-dom.development.js:7246
completeWork @ react-dom.development.js:19725
completeUnitOfWork @ react-dom.development.js:25963
performUnitOfWork @ react-dom.development.js:25759
workLoopConcurrent @ react-dom.development.js:25734
renderRootConcurrent @ react-dom.development.js:25690
performConcurrentWorkOnRoot @ react-dom.development.js:24504
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
page.tsx:60 Template loaded: {templateData: {…}, editableLayers: Array(2), json: {…}}
page.tsx:80 Setting initial customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:64 CustomizationEditor mounted with templateData: {width: 900, height: 1200, editableLayers: 2, hasJson: true, jsonLength: 722928}
customization-editor.tsx:76 Template JSON structure: {version: '5.3.0', objectCount: 6, objects: Array(6)}
customization-editor.tsx:578 Canvas init attempt 1/10: {containerWidth: 544, containerHeight: 437}
customization-editor.tsx:597 Canvas initialized with dimensions: 544 x 437
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:64 CustomizationEditor mounted with templateData: {width: 900, height: 1200, editableLayers: 2, hasJson: true, jsonLength: 722928}
customization-editor.tsx:76 Template JSON structure: {version: '5.3.0', objectCount: 6, objects: Array(6)}
customization-editor.tsx:578 Canvas init attempt 1/10: {containerWidth: 544, containerHeight: 511}
customization-editor.tsx:597 Canvas initialized with dimensions: 544 x 511
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1305 Cleaning up event listeners
use-load-state.ts:25 useLoadState: Attempting to load initial state
use-load-state.ts:28 useLoadState: Parsed JSON data: {objectCount: 6, version: '5.3.0'}
use-load-state.ts:37 useLoadState: Canvas dimensions: {canvasWidth: 544, canvasHeight: 548}
use-load-state.ts:44 useLoadState: Loading JSON into canvas...
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 1}
customization-editor.tsx:666 Editor is ready, triggering auto-zoom
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
page.tsx:60 Template loaded: {templateData: {…}, editableLayers: Array(2), json: {…}}
page.tsx:80 Setting initial customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 1}
customization-editor.tsx:666 Editor is ready, triggering auto-zoom
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:671 Canvas has 1 objects, checking for content...
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
use-load-state.ts:46 useLoadState: JSON loaded successfully, objects count: 6
use-load-state.ts:77 useLoadState: Calling autoZoom...
use-load-state.ts:79 useLoadState: Template loading complete
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:666 Editor is ready, triggering auto-zoom
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1470 Assigning ID layer_1753296466495 to text object: {type: 'textbox', left: 1049.21, top: 477.8, width: 315.59, height: 90.39999999999999}
customization-editor.tsx:1356 Trying to match image layer layer_1753346999128 (Image 2) with 3 available image objects
customization-editor.tsx:1357 Available image objects: (3) [{…}, {…}, {…}]
customization-editor.tsx:1372 Using stored properties for matching layer layer_1753346999128: {left: 856.66, top: 440.25, width: 1080, height: 720, scaleX: 0.34, …}
customization-editor.tsx:1397 Matched image by source URL for layer layer_1753346999128
customization-editor.tsx:1470 Assigning ID layer_1753346999128 to image object: {type: 'image', left: 841.49, top: 328.58, width: 1080, height: 720}
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:671 Canvas has 6 objects, checking for content...
customization-editor.tsx:674 Canvas has content, triggering auto-zoom
customization-editor.tsx:671 Canvas has 6 objects, checking for content...
customization-editor.tsx:674 Canvas has content, triggering auto-zoom
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:671 Canvas has 6 objects, checking for content...
customization-editor.tsx:674 Canvas has content, triggering auto-zoom
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1527 Auto-zooming after template load and ID assignment
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1527 Auto-zooming after template load and ID assignment
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1527 Auto-zooming after template load and ID assignment
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
customization-editor.tsx:1562 Clearing canvas selection
customization-editor.tsx:1323 Canvas loaded with 6 objects
customization-editor.tsx:1331 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
customization-editor.tsx:1484 Locking non-editable objects...
customization-editor.tsx:1505 Locked workspace object: rect
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:1516 Enabled interaction for editable object: image (ID: layer_1753346999128)
customization-editor.tsx:1516 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
customization-editor.tsx:1507 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:1507 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:793 Applying customizations: {layer_1753296466495: 'Heading'}
customization-editor.tsx:794 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:798 Processing layer layer_1753296466495 (text): Heading
customization-editor.tsx:802 Skipping layer layer_1753296466495 - no custom value or same as original
customization-editor.tsx:798 Processing layer layer_1753346999128 (image): undefined
customization-editor.tsx:802 Skipping layer layer_1753346999128 - no custom value or same as original
customization-editor.tsx:1305 Cleaning up event listeners
customization-editor.tsx:108 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
customization-editor.tsx:1300 Setting up event listeners for download and preview
customization-editor.tsx:1318 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
customization-editor.tsx:1544 External active layer changed: null
 Clearing canvas selection
 Canvas loaded with 6 objects
 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
 Locking non-editable objects...
 Locked workspace object: rect
 Locked non-editable object: image (ID: no-id)
 Enabled interaction for editable object: image (ID: layer_1753346999128)
 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
 Locked non-editable object: polygon (ID: no-id)
 Locked non-editable object: image (ID: no-id)
 Applying customizations: {layer_1753296466495: 'Heading'}
 Template editable layers: (2) [{…}, {…}]
 Processing layer layer_1753296466495 (text): Heading
 Skipping layer layer_1753296466495 - no custom value or same as original
 Processing layer layer_1753346999128 (image): undefined
 Skipping layer layer_1753346999128 - no custom value or same as original
 Canvas loaded with 6 objects
 Editable/relevant objects: (4) [{…}, {…}, {…}, {…}]
 Locking non-editable objects...
 Locked workspace object: rect
 Locked non-editable object: image (ID: no-id)
 Enabled interaction for editable object: image (ID: layer_1753346999128)
 Enabled interaction for editable object: textbox (ID: layer_1753296466495)
 Locked non-editable object: polygon (ID: no-id)
 Locked non-editable object: image (ID: no-id)
 Cleaning up event listeners
 Editor initialized: {hasCanvas: true, hasAutoZoom: true, canvasObjects: 6}
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: (2) ['layer_1753296466495', 'layer_1753346999128']
 External active layer changed: null
 Clearing canvas selection
