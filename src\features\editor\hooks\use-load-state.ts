import { fabric } from "fabric";
import { useEffect, useRef } from "react";

import { JSON_KEYS } from "@/features/editor/types";

interface UseLoadStateProps {
  autoZoom: () => void;
  canvas: fabric.Canvas | null;
  initialState: React.MutableRefObject<string | undefined>;
  canvasHistory: React.MutableRefObject<string[]>;
  setHistoryIndex: React.Dispatch<React.SetStateAction<number>>;
};

export const useLoadState = ({
  canvas,
  autoZoom,
  initialState,
  canvasHistory,
  setHistoryIndex,
}: UseLoadStateProps) => {
  const initialized = useRef(false);
  const loadingInProgress = useRef(false);

  useEffect(() => {
    // Prevent multiple simultaneous loading attempts
    if (initialized.current || loadingInProgress.current || !initialState?.current || !canvas) {
      if (loadingInProgress.current) {
        console.log('🎨 [TEMPLATE EDITOR] useLoadState: Loading already in progress, skipping');
      }
      return;
    }

    console.log('🎨 [TEMPLATE EDITOR] useLoadState: Attempting to load initial state');
    loadingInProgress.current = true;
    const loadStartTime = performance.now();

    try {
      const data = JSON.parse(initialState.current);
      console.log('🎨 [TEMPLATE EDITOR] useLoadState: Parsed JSON data:', {
        objectCount: data.objects?.length || 0,
        version: data.version,
        hasBackground: !!data.background,
        backgroundImage: data.backgroundImage,
        timestamp: new Date().toISOString()
      });

      // Log detailed object information
      if (data.objects && data.objects.length > 0) {
        console.log('🎨 [TEMPLATE EDITOR] useLoadState: Object details:',
          data.objects.map((obj: any) => ({
            type: obj.type,
            id: obj.id,
            name: obj.name,
            width: obj.width,
            height: obj.height,
            left: obj.left,
            top: obj.top
          }))
        );
      }

      // Validate canvas dimensions before loading
      const canvasWidth = canvas.getWidth();
      const canvasHeight = canvas.getHeight();

      console.log('🎨 [TEMPLATE EDITOR] useLoadState: Canvas dimensions:', {
        canvasWidth,
        canvasHeight,
        timestamp: new Date().toISOString()
      });

      if (canvasWidth <= 0 || canvasHeight <= 0) {
        console.warn("🎨 [TEMPLATE EDITOR] Canvas has invalid dimensions, skipping loadFromJSON", { canvasWidth, canvasHeight });
        loadingInProgress.current = false;
        return;
      }

      console.log('🎨 [TEMPLATE EDITOR] useLoadState: Loading JSON into canvas...');
      canvas.loadFromJSON(data, () => {
        const loadEndTime = performance.now();
        const loadDuration = loadEndTime - loadStartTime;

        console.log('🎨 [TEMPLATE EDITOR] useLoadState: JSON loaded successfully:', {
          objectsCount: canvas.getObjects().length,
          loadDurationMs: Math.round(loadDuration),
          timestamp: new Date().toISOString()
        });

          // Ensure all objects have valid dimensions after loading
          let fixedObjectsCount = 0;
          let textBaselineFixesCount = 0;

          canvas.getObjects().forEach(obj => {
            if (obj.width === 0 || obj.height === 0) {
              console.warn("🎨 [TEMPLATE EDITOR] Object has zero dimensions after loading:", {
                type: obj.type,
                id: obj.id,
                name: obj.name,
                width: obj.width,
                height: obj.height
              });
              // Set minimum dimensions to prevent rendering errors
              if (obj.width === 0) obj.set('width', 1);
              if (obj.height === 0) obj.set('height', 1);
              fixedObjectsCount++;
            }

            // Fix invalid textBaseline values
            if (obj.type === 'textbox' || obj.type === 'text' || obj.type === 'i-text') {
              const textObj = obj as fabric.Text;
              const currentBaseline = (textObj as any).textBaseline;
              if (currentBaseline && currentBaseline !== 'top' && currentBaseline !== 'hanging' &&
                  currentBaseline !== 'middle' && currentBaseline !== 'alphabetic' &&
                  currentBaseline !== 'ideographic' && currentBaseline !== 'bottom') {
                console.warn(`🎨 [TEMPLATE EDITOR] Invalid textBaseline value "${currentBaseline}" found, setting to "middle"`);
                (textObj as any).textBaseline = 'middle';
                textBaselineFixesCount++;
              }
            }
          });

          if (fixedObjectsCount > 0 || textBaselineFixesCount > 0) {
            console.log('🎨 [TEMPLATE EDITOR] Object fixes applied:', {
              fixedDimensions: fixedObjectsCount,
              fixedTextBaselines: textBaselineFixesCount
            });
          }

          const currentState = JSON.stringify(
            canvas.toJSON(JSON_KEYS),
          );

          canvasHistory.current = [currentState];
          setHistoryIndex(0);

          console.log('🎨 [TEMPLATE EDITOR] useLoadState: Calling autoZoom...');
          const autoZoomStartTime = performance.now();

          autoZoom();

          const autoZoomEndTime = performance.now();
          const autoZoomDuration = autoZoomEndTime - autoZoomStartTime;

          console.log('🎨 [TEMPLATE EDITOR] useLoadState: Template loading complete:', {
            totalLoadDurationMs: Math.round(loadEndTime - loadStartTime),
            autoZoomDurationMs: Math.round(autoZoomDuration),
            finalObjectCount: canvas.getObjects().length,
            canvasSize: { width: canvas.width, height: canvas.height },
            timestamp: new Date().toISOString()
          });

        // Ensure all objects remain interactive after loading
        setTimeout(() => {
          const objects = canvas.getObjects();
          objects.forEach(obj => {
            if (obj.name !== "clip") {
              obj.set({
                selectable: true,
                evented: true,
                hasControls: true,
                hasBorders: true
              });
            }
          });
          console.log('🎨 [TEMPLATE EDITOR] Object interactivity verified after auto-zoom');
        }, 200);
      }); // canvas.loadFromJSON callback closing

      initialized.current = true;
      loadingInProgress.current = false;
    } catch (error) {
      console.error("🎨 [TEMPLATE EDITOR] Error loading canvas state:", error);
      initialized.current = true; // Prevent infinite retries
      loadingInProgress.current = false;
    }
  },
  [
    canvas,
    autoZoom,
    initialState, // no need, this is a ref
    canvasHistory, // no need, this is a ref
    setHistoryIndex, // no need, this is a dispatch
  ]);
};
