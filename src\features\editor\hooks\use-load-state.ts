import { fabric } from "fabric";
import { useEffect, useRef } from "react";

import { JSON_KEYS } from "@/features/editor/types";

interface UseLoadStateProps {
  autoZoom: () => void;
  canvas: fabric.Canvas | null;
  initialState: React.MutableRefObject<string | undefined>;
  canvasHistory: React.MutableRefObject<string[]>;
  setHistoryIndex: React.Dispatch<React.SetStateAction<number>>;
};

// Global flag to prevent multiple content loading across all instances
let globalContentLoaded = false;

export const useLoadState = ({
  canvas,
  autoZoom,
  initialState,
  canvasHistory,
  setHistoryIndex,
}: UseLoadStateProps) => {
  const initialized = useRef(false);

  useEffect(() => {
    // STOP ALL CONTENT LOADING after first success
    if (globalContentLoaded) {
      console.log('🎨 [TEMPLATE EDITOR] ❌ BLOCKING CONTENT RELOAD - Content already loaded globally');
      return;
    }

    if (!initialized.current && initialState?.current && canvas) {
      console.log('🎨 [TEMPLATE EDITOR] useLoadState: Attempting to load initial state');
      const loadStartTime = performance.now();

      // Mark as initialized immediately to prevent multiple calls
      initialized.current = true;
      globalContentLoaded = true; // GLOBAL LOCK - no more content loading allowed

      try {
        const data = JSON.parse(initialState.current);
        console.log('🎨 [TEMPLATE EDITOR] useLoadState: Parsed JSON data:', {
          objectCount: data.objects?.length || 0,
          version: data.version,
          hasBackground: !!data.background,
          backgroundImage: data.backgroundImage,
          timestamp: new Date().toISOString()
        });

        // Log detailed object information
        if (data.objects && data.objects.length > 0) {
          console.log('🎨 [TEMPLATE EDITOR] useLoadState: Object details:',
            data.objects.map((obj: any) => ({
              type: obj.type,
              id: obj.id,
              name: obj.name,
              width: obj.width,
              height: obj.height,
              left: obj.left,
              top: obj.top
            }))
          );
        }

        // Validate canvas dimensions before loading
        const canvasWidth = canvas.getWidth();
        const canvasHeight = canvas.getHeight();

        console.log('🎨 [TEMPLATE EDITOR] useLoadState: Canvas dimensions:', {
          canvasWidth,
          canvasHeight,
          timestamp: new Date().toISOString()
        });

        if (canvasWidth <= 0 || canvasHeight <= 0) {
          console.warn("🎨 [TEMPLATE EDITOR] Canvas has invalid dimensions, skipping loadFromJSON", { canvasWidth, canvasHeight });
          return;
        }

        console.log('🎨 [TEMPLATE EDITOR] useLoadState: Loading JSON into canvas...');
        canvas.loadFromJSON(data, () => {
          const loadEndTime = performance.now();
          const loadDuration = loadEndTime - loadStartTime;

          console.log('🎨 [TEMPLATE EDITOR] useLoadState: JSON loaded successfully:', {
            objectsCount: canvas.getObjects().length,
            loadDurationMs: Math.round(loadDuration),
            timestamp: new Date().toISOString()
          });

          // Ensure all objects have valid dimensions after loading
          let fixedObjectsCount = 0;
          let textBaselineFixesCount = 0;

          canvas.getObjects().forEach(obj => {
            if (obj.width === 0 || obj.height === 0) {
              console.warn("🎨 [TEMPLATE EDITOR] Object has zero dimensions after loading:", {
                type: obj.type,
                id: obj.id,
                name: obj.name,
                width: obj.width,
                height: obj.height
              });
              // Set minimum dimensions to prevent rendering errors
              if (obj.width === 0) obj.set('width', 1);
              if (obj.height === 0) obj.set('height', 1);
              fixedObjectsCount++;
            }

            // Fix invalid textBaseline values
            if (obj.type === 'textbox' || obj.type === 'text' || obj.type === 'i-text') {
              const textObj = obj as fabric.Text;
              const currentBaseline = (textObj as any).textBaseline;
              if (currentBaseline && currentBaseline !== 'top' && currentBaseline !== 'hanging' &&
                  currentBaseline !== 'middle' && currentBaseline !== 'alphabetic' &&
                  currentBaseline !== 'ideographic' && currentBaseline !== 'bottom') {
                console.warn(`🎨 [TEMPLATE EDITOR] Invalid textBaseline value "${currentBaseline}" found, setting to "middle"`);
                (textObj as any).textBaseline = 'middle';
                textBaselineFixesCount++;
              }
            }
          });

          if (fixedObjectsCount > 0 || textBaselineFixesCount > 0) {
            console.log('🎨 [TEMPLATE EDITOR] Object fixes applied:', {
              fixedDimensions: fixedObjectsCount,
              fixedTextBaselines: textBaselineFixesCount
            });
          }

          const currentState = JSON.stringify(
            canvas.toJSON(JSON_KEYS),
          );

          canvasHistory.current = [currentState];
          setHistoryIndex(0);

          console.log('🎨 [TEMPLATE EDITOR] useLoadState: SKIPPING AUTO-ZOOM to prevent breakage');

          // Ensure all objects are interactive WITHOUT auto-zoom
          canvas.getObjects().forEach(obj => {
            if (obj.name !== "clip") { // Don't make the workspace clip interactive
              obj.selectable = true;
              obj.evented = true;
              obj.interactive = true;
            }
          });

          // Ensure canvas is interactive
          canvas.selection = true;
          canvas.interactive = true;
          canvas.evented = true;

          // Just render without auto-zoom
          canvas.requestRenderAll();
          console.log('🎨 [TEMPLATE EDITOR] ✅ SKIPPED AUTO-ZOOM - Canvas should remain responsive');

          // Final verification without auto-zoom
          setTimeout(() => {
            canvas.getObjects().forEach(obj => {
              if (obj.name !== "clip") {
                obj.selectable = true;
                obj.evented = true;
                obj.interactive = true;
              }
            });

            // Ensure canvas remains interactive
            canvas.selection = true;
            canvas.interactive = true;
            canvas.evented = true;

            canvas.requestRenderAll();
            console.log('🎨 [TEMPLATE EDITOR] ✅ PAGE FULLY LOADED AND READY FOR INTERACTION ✅');
            console.log('🎨 [TEMPLATE EDITOR] Object interactivity verified WITHOUT auto-zoom');
          }, 200);

          const loadEndTime = performance.now();

          console.log('🎨 [TEMPLATE EDITOR] useLoadState: Template loading complete WITHOUT AUTO-ZOOM:', {
            totalLoadDurationMs: Math.round(loadEndTime - loadStartTime),
            finalObjectCount: canvas.getObjects().length,
            canvasSize: { width: canvas.width, height: canvas.height },
            timestamp: new Date().toISOString()
          });
        });
      } catch (error) {
        console.error("🎨 [TEMPLATE EDITOR] Error loading canvas state:", error);
        // Keep initialized as true to prevent infinite retries
      }
    }
  },
  [
    canvas,
    autoZoom,
    initialState, // no need, this is a ref
    canvasHistory, // no need, this is a ref
    setHistoryIndex, // no need, this is a dispatch
  ]);
};
