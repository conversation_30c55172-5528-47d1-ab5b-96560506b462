import { fabric } from "fabric";
import { useCallback, useEffect } from "react";

interface UseAutoResizeProps {
  canvas: fabric.Canvas | null;
  container: HTMLDivElement | null;
}

export const useAutoResize = ({ canvas, container }: UseAutoResizeProps) => {
  const autoZoom = useCallback(() => {
    console.log('🎨 [TEMPLATE EDITOR] autoZoom called');
    const autoZoomStartTime = performance.now();

    if (!canvas || !container) {
      console.warn('🎨 [TEMPLATE EDITOR] autoZoom: Missing canvas or container');
      return;
    }

    const width = container.offsetWidth;
    const height = container.offsetHeight;

    console.log('🎨 [TEMPLATE EDITOR] autoZoom: Container dimensions:', {
      width,
      height,
      timestamp: new Date().toISOString()
    });

    // Ensure we have valid dimensions
    if (width <= 0 || height <= 0) {
      console.warn("🎨 [TEMPLATE EDITOR] Container has invalid dimensions for autoZoom", { width, height });
      return;
    }

    // Don't set canvas dimensions here - they should be set by ResizeObserver
    // canvas.setWidth(width);
    // canvas.setHeight(height);

    const center = canvas.getCenter();
    console.log('🎨 [TEMPLATE EDITOR] autoZoom: Canvas center:', center);

    const zoomRatio = 0.85;
    const localWorkspace = canvas
      .getObjects()
      .find((object) => object.name === "clip");

    if (!localWorkspace) {
      console.warn('🎨 [TEMPLATE EDITOR] autoZoom: No workspace found');
      return;
    }

    // Validate workspace dimensions
    const workspaceWidth = localWorkspace.width || 0;
    const workspaceHeight = localWorkspace.height || 0;

    console.log('🎨 [TEMPLATE EDITOR] autoZoom: Workspace dimensions:', {
      workspaceWidth,
      workspaceHeight
    });

    if (workspaceWidth <= 0 || workspaceHeight <= 0) {
      console.warn("🎨 [TEMPLATE EDITOR] Workspace has invalid dimensions for autoZoom", { workspaceWidth, workspaceHeight });
      return;
    }

    // @ts-ignore
    const scale = fabric.util.findScaleToFit(localWorkspace, {
      width: width,
      height: height,
    });

    const zoom = zoomRatio * scale;

    console.log('🎨 [TEMPLATE EDITOR] autoZoom: Calculated zoom values:', {
      scale,
      zoomRatio,
      finalZoom: zoom
    });

    canvas.setViewportTransform(fabric.iMatrix.concat());
    canvas.zoomToPoint(new fabric.Point(center.left, center.top), zoom);

    const workspaceCenter = localWorkspace.getCenterPoint();
    const viewportTransform = canvas.viewportTransform;

    console.log('🎨 [TEMPLATE EDITOR] autoZoom: Workspace center:', workspaceCenter);

    if (
      canvas.width === undefined ||
      canvas.height === undefined ||
      !viewportTransform
    ) {
      console.warn('🎨 [TEMPLATE EDITOR] autoZoom: Invalid canvas dimensions or viewport transform');
      return;
    }

    viewportTransform[4] = canvas.width / 2 - workspaceCenter.x * viewportTransform[0];
    viewportTransform[5] = canvas.height / 2 - workspaceCenter.y * viewportTransform[3];

    canvas.setViewportTransform(viewportTransform);

    localWorkspace.clone((cloned: fabric.Rect) => {
      canvas.clipPath = cloned;
      canvas.requestRenderAll();

      const autoZoomEndTime = performance.now();
      const autoZoomDuration = autoZoomEndTime - autoZoomStartTime;

      console.log('🎨 [TEMPLATE EDITOR] autoZoom completed successfully:', {
        durationMs: Math.round(autoZoomDuration),
        finalZoom: canvas.getZoom(),
        canvasSize: { width: canvas.width, height: canvas.height },
        viewportTransform: canvas.viewportTransform,
        timestamp: new Date().toISOString()
      });

      // Ensure canvas remains interactive after auto-zoom
      setTimeout(() => {
        // Verify object interactivity
        const objects = canvas.getObjects();
        let interactiveCount = 0;
        objects.forEach(obj => {
          if (obj.name !== "clip" && obj.selectable !== false) {
            obj.set({
              selectable: true,
              evented: true,
              hasControls: true,
              hasBorders: true
            });
            interactiveCount++;
          }
        });

        console.log('🎨 [TEMPLATE EDITOR] Final interaction verification completed:', {
          totalObjects: objects.length,
          interactiveObjects: interactiveCount,
          canvasZoom: canvas.getZoom()
        });

        // Mark the template editor as fully loaded and ready
        console.log('🎨 [TEMPLATE EDITOR] ✅ PAGE FULLY LOADED AND READY FOR INTERACTION ✅');
      }, 100);
    });
  }, [canvas, container]);

  // Disabled automatic resize observer to prevent canvas interaction issues
  // The ResizeObserver was causing continuous auto-zoom operations that interfered
  // with canvas interactivity. Manual zoom controls are still available.
  useEffect(() => {
    // ResizeObserver disabled to maintain canvas interactivity
    // Users can manually adjust zoom using the zoom controls
    return () => {
      // Cleanup function (no-op since ResizeObserver is disabled)
    };
  }, [canvas, container, autoZoom]);

  return { autoZoom };
};
