import { fabric } from "fabric";
import { useCallback, useEffect, useRef } from "react";

interface UseAutoResizeProps {
  canvas: fabric.Canvas | null;
  container: HTMLDivElement | null;
}

export const useAutoResize = ({ canvas, container }: UseAutoResizeProps) => {
  const lastAutoZoomRef = useRef<number>(0);
  const autoZoomThrottleMs = 1000; // Prevent auto-zoom from being called more than once per second
  const autoZoomDisabled = useRef(false); // Flag to disable auto-zoom if it causes issues

  const autoZoom = useCallback(() => {
    if (autoZoomDisabled.current) {
      console.log('🎨 [TEMPLATE EDITOR] autoZoom disabled - skipping');
      return;
    }

    const now = Date.now();
    if (now - lastAutoZoomRef.current < autoZoomThrottleMs) {
      console.log('🎨 [TEMPLATE EDITOR] autoZoom throttled - too soon since last call');
      return;
    }
    lastAutoZoomRef.current = now;

    console.log('🎨 [TEMPLATE EDITOR] autoZoom called');
    const autoZoomStartTime = performance.now();

    if (!canvas || !container) {
      console.warn('🎨 [TEMPLATE EDITOR] autoZoom: Missing canvas or container');
      return;
    }

    const width = container.offsetWidth;
    const height = container.offsetHeight;

    console.log('🎨 [TEMPLATE EDITOR] autoZoom: Container dimensions:', {
      width,
      height,
      timestamp: new Date().toISOString()
    });

    // Ensure we have valid dimensions
    if (width <= 0 || height <= 0) {
      console.warn("🎨 [TEMPLATE EDITOR] Container has invalid dimensions for autoZoom", { width, height });
      return;
    }

    // Don't set canvas dimensions here - they should be set by ResizeObserver
    // canvas.setWidth(width);
    // canvas.setHeight(height);

    const center = canvas.getCenter();
    console.log('🎨 [TEMPLATE EDITOR] autoZoom: Canvas center:', center);

    const zoomRatio = 0.85;
    const localWorkspace = canvas
      .getObjects()
      .find((object) => object.name === "clip");

    if (!localWorkspace) {
      console.warn('🎨 [TEMPLATE EDITOR] autoZoom: No workspace found');
      return;
    }

    // Validate workspace dimensions
    const workspaceWidth = localWorkspace.width || 0;
    const workspaceHeight = localWorkspace.height || 0;

    console.log('🎨 [TEMPLATE EDITOR] autoZoom: Workspace dimensions:', {
      workspaceWidth,
      workspaceHeight
    });

    if (workspaceWidth <= 0 || workspaceHeight <= 0) {
      console.warn("🎨 [TEMPLATE EDITOR] Workspace has invalid dimensions for autoZoom", { workspaceWidth, workspaceHeight });
      return;
    }

    // @ts-ignore
    const scale = fabric.util.findScaleToFit(localWorkspace, {
      width: width,
      height: height,
    });

    const zoom = zoomRatio * scale;

    console.log('🎨 [TEMPLATE EDITOR] autoZoom: Calculated zoom values:', {
      scale,
      zoomRatio,
      finalZoom: zoom
    });

    // Ensure zoom level is reasonable for interaction (not too small)
    const minZoom = 0.3; // Increased minimum zoom for better interaction
    const maxZoom = 2.0; // Reduced maximum zoom
    const clampedZoom = Math.max(minZoom, Math.min(maxZoom, zoom));

    if (clampedZoom !== zoom) {
      console.log('🎨 [TEMPLATE EDITOR] autoZoom: Clamping zoom from', zoom, 'to', clampedZoom);
    }

    // Store current canvas state before zoom
    const wasInteractive = canvas.interactive;
    const wasEvented = canvas.evented;
    const wasSelection = canvas.selection;

    canvas.setViewportTransform(fabric.iMatrix.concat());
    canvas.zoomToPoint(new fabric.Point(center.left, center.top), clampedZoom);

    // Restore canvas interaction state immediately
    canvas.interactive = wasInteractive;
    canvas.evented = wasEvented;
    canvas.selection = wasSelection;

    const workspaceCenter = localWorkspace.getCenterPoint();
    const viewportTransform = canvas.viewportTransform;

    console.log('🎨 [TEMPLATE EDITOR] autoZoom: Workspace center:', workspaceCenter);

    if (
      canvas.width === undefined ||
      canvas.height === undefined ||
      !viewportTransform
    ) {
      console.warn('🎨 [TEMPLATE EDITOR] autoZoom: Invalid canvas dimensions or viewport transform');
      return;
    }

    viewportTransform[4] = canvas.width / 2 - workspaceCenter.x * viewportTransform[0];
    viewportTransform[5] = canvas.height / 2 - workspaceCenter.y * viewportTransform[3];

    canvas.setViewportTransform(viewportTransform);

    // Use a simpler approach that's less likely to break interaction
    canvas.clipPath = localWorkspace;

    // Ensure canvas is properly interactive after auto-zoom
    canvas.selection = true;
    canvas.interactive = true;
    canvas.evented = true;

    // Ensure all objects (except workspace) are interactive
    canvas.getObjects().forEach(obj => {
      if (obj.name !== "clip") {
        obj.selectable = true;
        obj.evented = true;
        obj.interactive = true;
      }
    });

    // Force a render to ensure everything is properly displayed
    canvas.requestRenderAll();

    const autoZoomEndTime = performance.now();
    const autoZoomDuration = autoZoomEndTime - autoZoomStartTime;

    console.log('🎨 [TEMPLATE EDITOR] autoZoom completed successfully:', {
      durationMs: Math.round(autoZoomDuration),
      finalZoom: canvas.getZoom(),
      clampedZoom,
      canvasSize: { width: canvas.width, height: canvas.height },
      viewportTransform: canvas.viewportTransform,
      canvasInteractive: canvas.interactive,
      canvasEvented: canvas.evented,
      canvasSelection: canvas.selection,
      interactiveObjects: canvas.getObjects().filter(obj => obj.name !== "clip" && obj.selectable).length,
      timestamp: new Date().toISOString()
    });

    // Small delay to ensure canvas is fully ready for interaction
    setTimeout(() => {
      // Final verification of interaction properties
      canvas.selection = true;
      canvas.interactive = true;
      canvas.evented = true;

      canvas.getObjects().forEach(obj => {
        if (obj.name !== "clip") {
          obj.selectable = true;
          obj.evented = true;
          obj.interactive = true;
        }
      });

      canvas.requestRenderAll();
      console.log('🎨 [TEMPLATE EDITOR] Final interaction verification completed');

      // Mark the template editor as fully loaded and ready
      console.log('🎨 [TEMPLATE EDITOR] ✅ PAGE FULLY LOADED AND READY FOR INTERACTION ✅');
    }, 150);
  }, [canvas, container]);

  // Disabled automatic resize observer to prevent canvas interaction issues
  // The ResizeObserver was causing continuous auto-zoom operations that interfered
  // with canvas interactivity. Manual zoom controls are still available.
  useEffect(() => {
    // ResizeObserver disabled to maintain canvas interactivity
    // Users can manually adjust zoom using the zoom controls
    return () => {
      // Cleanup function (no-op since ResizeObserver is disabled)
    };
  }, [canvas, container, autoZoom]);

  return { autoZoom };
};
